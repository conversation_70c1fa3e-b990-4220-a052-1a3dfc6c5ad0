{"_last_used_preset": "Preset: Factures", "Preset: Factures": {"document_type": "FA", "source_folder": "//************/tarec/Archive/SCANNER/FACTURE RECUES", "excel_file": "//************/tarec/Archive/Archive 2023/SUIVI OV CHQ OPCVM BC 2023.xlsx", "excel_sheet": "FACTURE", "processed_folder": "//************/tarec/Archive/Archive 2023/FACTURE RECU", "skip_folder": "//************/tarec/Archive/SCANNER/FACTURE RECUES/SKIPPED", "output_template": "{processed_folder}/{filter3|date.year}/MOIS {filter3|date.month}/{filter2|str.first_word} {filter1} {filter2|str.split_no_last}.pdf", "filter1_column": "FOURNISSEUR", "filter2_column": "N° FACTURE", "filter3_column": "DATE FACTURE", "filter4_column": "MONTANT", "prompt": "Extract the following information from this scanned invoice:\n1. Supplier/Vendor Name\n2. Invoice Number\n3. Invoice Date (in format DD/MM/YYYY)\n4. Total Amount (without currency and without thousand separators)\n\nReturn ONLY a JSON object with these fields as keys:\n{\n    \"supplier_name\": \"extracted supplier name\",\n    \"invoice_number\": \"extracted invoice number\",\n    \"invoice_date\": \"extracted date in DD/MM/YYYY format\",\n    \"total_amount\": \"extracted amount without currency and without thousand separators\"\n}\n\nIf any field is not found in the image, set its value to null.", "field_mappings": {"supplier_name": "filter1", "invoice_number": "filter2", "invoice_date": "filter3", "total_amount": "filter4"}, "vision": {"enabled": true, "gemini_api_key": "AIzaSyDK1h7R1fdjMkT57lZDUXDHOVz3bgohcYo", "model": "gemini-2.5-flash", "supplier_match_threshold": 0.75, "default_language": "fr", "ocr_preprocessing": true}}}